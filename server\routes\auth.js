const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Generate JWT Token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '7d' });
};

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validation
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 6 characters long'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists with this email'
      });
    }

    // Create new user
    const user = new User({ name, email, password });
    await user.save();

    // Generate token
    const token = generateToken(user._id);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      token,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration'
    });
  }
});

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Fallback authentication for development (when MongoDB is not available)
    const fallbackUsers = [
      {
        id: 'admin-001',
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        isAdmin: true
      },
      {
        id: 'user-001',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'user123',
        isAdmin: false
      }
    ];

    // Check fallback users first
    const fallbackUser = fallbackUsers.find(u => u.email === email && u.password === password);
    if (fallbackUser) {
      const token = generateToken(fallbackUser.id);
      return res.json({
        success: true,
        message: 'Login successful (development mode)',
        token,
        user: {
          id: fallbackUser.id,
          name: fallbackUser.name,
          email: fallbackUser.email,
          isAdmin: fallbackUser.isAdmin
        }
      });
    }

    // Try database authentication if MongoDB is available
    try {
      const user = await User.findOne({ email });
      if (!user) {
        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check password
      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return res.status(400).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Generate token
      const token = generateToken(user._id);

      res.json({
        success: true,
        message: 'Login successful',
        token,
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          isAdmin: user.isAdmin
        }
      });
    } catch (dbError) {
      // If database is not available, return error for non-fallback users
      return res.status(400).json({
        success: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
});

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', auth, async (req, res) => {
  try {
    // Fallback users for development
    const fallbackUsers = [
      {
        id: 'admin-001',
        name: 'Admin User',
        email: '<EMAIL>',
        isAdmin: true,
        registeredEvents: []
      },
      {
        id: 'user-001',
        name: 'Test User',
        email: '<EMAIL>',
        isAdmin: false,
        registeredEvents: []
      }
    ];

    // Check if this is a fallback user
    const fallbackUser = fallbackUsers.find(u => u.id === req.user._id);
    if (fallbackUser) {
      return res.json({
        success: true,
        user: fallbackUser
      });
    }

    // Try database lookup for real users
    try {
      const user = await User.findById(req.user._id)
        .select('-password')
        .populate('registeredEvents', 'title category eventDate venue');

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        user
      });
    } catch (dbError) {
      // If database is not available and not a fallback user
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
